const express = require('express');
const cors = require('cors');
const path = require('path');
const fs = require('fs');

const app = express();
const PORT = process.env.API_PORT || 3001;

// Middleware
app.use(cors());
app.use(express.json());

// Simple health check
app.get('/api/health', (req, res) => {
  res.json({ 
    status: 'OK', 
    timestamp: new Date().toISOString(),
    network: 'boredomcore'
  });
});

// Mock contract creation endpoints
app.post('/api/create-token-contract', (req, res) => {
  setTimeout(() => {
    const mockAddress = '0x' + Array.from({length: 40}, () => Math.floor(Math.random() * 16).toString(16)).join('');
    res.json({
      success: true,
      contractAddress: mockAddress,
      transactionHash: '0x' + Array.from({length: 64}, () => Math.floor(Math.random() * 16).toString(16)).join('')
    });
  }, 2000);
});

app.post('/api/create-nft-contract', (req, res) => {
  setTimeout(() => {
    const mockAddress = '0x' + Array.from({length: 40}, () => Math.floor(Math.random() * 16).toString(16)).join('');
    res.json({
      success: true,
      contractAddress: mockAddress,
      transactionHash: '0x' + Array.from({length: 64}, () => Math.floor(Math.random() * 16).toString(16)).join('')
    });
  }, 2000);
});

// Mock minting endpoints
app.post('/api/mint-token', (req, res) => {
  setTimeout(() => {
    res.json({
      success: true,
      txHash: '0x' + Array.from({length: 64}, () => Math.floor(Math.random() * 16).toString(16)).join('')
    });
  }, 1500);
});

app.post('/api/mint-nft', (req, res) => {
  setTimeout(() => {
    res.json({
      success: true,
      txHash: '0x' + Array.from({length: 64}, () => Math.floor(Math.random() * 16).toString(16)).join(''),
      tokenId: Math.floor(Math.random() * 10000).toString()
    });
  }, 1500);
});

// Mock contracts list
app.get('/api/contracts', (req, res) => {
  res.json([]);
});

app.listen(PORT, () => {
  console.log(`Ghost Developer Studio API running on port ${PORT}`);
});

module.exports = app;
