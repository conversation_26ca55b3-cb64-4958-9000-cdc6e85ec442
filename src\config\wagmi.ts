import { getDefaultConfig } from '@rainbow-me/rainbowkit'
import { boredomcore, sepolia } from './chains'

export const config = getDefaultConfig({
  appName: 'Ghost Developer Studio',
  projectId: '2f5a2b1c8e9d3f4a5b6c7d8e9f0a1b2c', // Placeholder - get real one from https://cloud.walletconnect.com
  chains: [boredomcore, sepolia],
  ssr: false, // If your dApp uses server side rendering (SSR)
})

export { boredomcore, sepolia }
