require("@nomicfoundation/hardhat-toolbox");
require("dotenv").config();

/** @type import('hardhat/config').HardhatUserConfig */
module.exports = {
  solidity: {
    version: "0.8.19",
    settings: {
      optimizer: {
        enabled: true,
        runs: 200,
      },
    },
  },
  networks: {
    hardhat: {
      chainId: 31337,
    },
    boredomcore: {
      url: process.env.VITE_RPC_URL || "https://rpc.devnet.alchemy.com/4108ef95-2a6e-41d8-ab30-9babbc45bea5",
      accounts: process.env.PRIVATE_KEY ? [process.env.PRIVATE_KEY] : [],
      chainId: ****************,
    },
    sepolia: {
      url: process.env.VITE_PARENT_RPC_URL || "https://eth-sepolia.g.alchemy.com/v2/demo",
      accounts: process.env.PRIVATE_KEY ? [process.env.PRIVATE_KEY] : [],
      chainId: ********,
    },
  },
  etherscan: {
    apiKey: {
      boredomcore: "abc", // Placeholder for custom network
    },
    customChains: [
      {
        network: "boredomcore",
        chainId: ****************,
        urls: {
          apiURL: "https://explorer-****************.devnet.alchemy.com/api",
          browserURL: "https://explorer-****************.devnet.alchemy.com"
        }
      }
    ]
  },
  paths: {
    sources: "./contracts",
    tests: "./test",
    cache: "./cache",
    artifacts: "./artifacts"
  },
};
